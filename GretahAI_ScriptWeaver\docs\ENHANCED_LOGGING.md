# Enhanced Logging System for Stage 10 (Script Playground)

## Overview

The enhanced logging system provides comprehensive debug information for Stage 10 operations in GretahAI ScriptWeaver. It captures detailed logs across all workflows including interactive element selection, gap analysis, script generation, session state management, and user interface interactions.

## Features

### 🔍 **Real-time Debug Information**
- Live debug panel in Streamlit UI when debug mode is enabled
- Timestamped log entries with context and data
- Filterable logs by level and context
- Automatic log rotation and cleanup

### 📊 **Performance Monitoring**
- Operation timing with millisecond precision
- AI request/response duration tracking
- Process execution monitoring
- Resource usage tracking

### 🐛 **Comprehensive Error Tracking**
- Detailed error context and stack traces
- Error categorization by workflow
- Recovery procedure logging
- Exception handling with context preservation

### 📈 **Usage Analytics**
- User interaction pattern tracking
- Workflow completion rates
- Feature usage statistics
- Session state evolution tracking

## Architecture

### Core Components

1. **Stage10Logger Class** (`core/stage10_logger.py`)
   - Main logging coordinator
   - Multiple output targets (file, UI, console)
   - Context-aware logging with data attachment

2. **Specialized Logging Functions**
   - `log_interactive_selector()` - Interactive element selector events
   - `log_gap_analysis()` - Gap analysis workflow events
   - `log_script_generation()` - Script generation events
   - `log_session_state()` - Session state management
   - `log_ui_interaction()` - User interface interactions
   - `log_process()` - Subprocess and process management
   - `log_ai_request()` - AI API requests and responses

3. **Operation Context Manager**
   - `log_operation()` - Context manager for timing operations
   - Nested operation support
   - Automatic timing and error handling

## Usage Guide

### Basic Setup

```python
from core.stage10_logger import (
    log_gap_analysis, log_interactive_selector, 
    log_session_state, log_ui_interaction, 
    log_operation, display_debug_panel
)
```

### Logging Specific Events

#### Interactive Selector Events
```python
# Log selector launch
log_interactive_selector("launch_start", gap_id, 
                        description="Username field selector",
                        website_url="https://example.com")

# Log process launch
log_interactive_selector("process_launched", gap_id,
                        pid=12345,
                        stdout_log="/tmp/selector_stdout.log")

# Log errors
log_interactive_selector("launch_error", gap_id, 
                        error="Browser launch failed")
```

#### Gap Analysis Events
```python
# Log analysis start
log_gap_analysis("analysis_start",
                template_id="template_123",
                target_id="test_case_456",
                model="gemini-2.0-flash")

# Log AI response
log_gap_analysis("ai_response_received",
                response_length=2500,
                duration_ms=1250.5)

# Log completion
log_gap_analysis("analysis_completed",
                gaps_found=3,
                confidence="high")
```

#### Script Generation Events
```python
# Log generation start
log_script_generation("generation_start",
                     template_id="template_123",
                     target_test_case="test_case_456")

# Log completion
log_script_generation("generation_completed",
                     script_length=8000,
                     duration_ms=3500.2,
                     success=True)
```

#### Session State Events
```python
# Log state updates
log_session_state("state_update", "website_url", "https://example.com")

# Log cleanup
log_session_state("state_cleanup", "old_session_key", None)
```

#### UI Interaction Events
```python
# Log button clicks
log_ui_interaction("button_click", "generate_script_button",
                  button_type="primary",
                  section="script_generation")

# Log form submissions
log_ui_interaction("form_submission", "gap_filling_form",
                  form_data_count=5,
                  validation_passed=True)
```

#### Process Events
```python
# Log process start
log_process("process_start",
           command="python test_script.py",
           pid=12345,
           working_directory="/tmp")

# Log completion
log_process("process_completed",
           pid=12345,
           exit_code=0,
           duration_ms=2500.0)
```

#### AI Request Events
```python
# Log successful request
log_ai_request("gap_analysis", "gemini-2.0-flash", 1500,
              response_length=2000,
              duration_ms=1200.5)

# Log failed request
log_ai_request("script_generation", "gemini-2.0-flash", 3000,
              error="Rate limit exceeded")
```

### Operation Context Management

```python
# Time an operation
with log_operation("generate_script_from_template", "script_generation"):
    # Your code here
    template_analysis = analyze_template(template)
    
    # Nested operations are supported
    with log_operation("ai_generation", "script_generation.ai"):
        generated_script = call_ai_api(prompt)
    
    # Any exceptions are automatically logged with context
    save_script(generated_script)
```

### Debug Panel Display

```python
# In your Streamlit app
from core.stage10_logger import display_debug_panel

def my_stage_function():
    # Your stage logic here
    
    # Display debug panel (only shows when debug mode is enabled)
    display_debug_panel()
```

## Configuration

### Enable Debug Mode

Set the environment variable:
```bash
export SCRIPTWEAVER_DEBUG=true
```

Or in Python:
```python
import os
os.environ["SCRIPTWEAVER_DEBUG"] = "true"
```

### Log File Locations

- **Session Logs**: `debug_logs/stage10/stage10_session_YYYYMMDD_HHMMSS.log`
- **Export Files**: `debug_logs/stage10/stage10_export_YYYYMMDD_HHMMSS.json`

### Log Retention

- Session state logs: Last 100 entries
- File logs: Automatic rotation (no size limit currently)
- Export files: Manual cleanup required

## Debug Panel Features

### Real-time Log Viewing
- Live updates as events occur
- Color-coded log levels (🔍 DEBUG, ℹ️ INFO, ⚠️ WARNING, ❌ ERROR)
- Expandable data sections for complex log entries

### Filtering Options
- **Log Level Filter**: Show only specific log levels
- **Context Filter**: Filter by operation context
- **Recent Entries**: Shows last 20 filtered entries

### Export Functionality
- Export current session logs to JSON
- Includes metadata and timestamps
- Suitable for offline analysis

## Integration with Existing Code

### Gap Analysis Integration

The gap analysis module (`core/gap_analysis.py`) has been enhanced with comprehensive logging:

```python
# Automatic logging in analyze_template_gaps()
with log_operation("analyze_template_gaps", "gap_analysis"):
    log_gap_analysis("analysis_start", template_id=template_id, target_id=target_id)
    # ... analysis logic ...
    log_gap_analysis("analysis_completed", gaps_found=len(gaps))
```

### Interactive Selector Integration

Interactive selector functions now include detailed process logging:

```python
# Automatic logging in _launch_interactive_selector_for_gap()
with log_operation(f"launch_interactive_selector_{gap_id}", "interactive_selector"):
    log_interactive_selector("launch_start", gap_id, description=description)
    # ... launch logic ...
    log_interactive_selector("process_launched", gap_id, pid=process.pid)
```

### Stage 10 Integration

Stage 10 main function includes comprehensive workflow logging:

```python
# Automatic logging in stage10_script_playground()
with log_operation("stage10_script_playground", "stage10"):
    log_ui_interaction("stage_accessed", "stage10_script_playground")
    # ... stage logic ...
    display_debug_panel()  # Shows debug panel when enabled
```

## Performance Impact

### Minimal Overhead
- Logging operations are lightweight
- Debug mode can be disabled in production
- File I/O is asynchronous where possible
- Memory usage is controlled with log rotation

### Debug Mode Only Features
- UI debug panel only appears when `SCRIPTWEAVER_DEBUG=true`
- Detailed data logging only occurs in debug mode
- Console output controlled by debug flag

## Troubleshooting

### Common Issues

1. **Debug Panel Not Showing**
   - Ensure `SCRIPTWEAVER_DEBUG=true` is set
   - Check that `display_debug_panel()` is called in your code
   - Verify no import errors in stage10_logger module

2. **Logs Not Appearing**
   - Check debug mode is enabled
   - Verify log directory permissions
   - Check for import errors

3. **Performance Issues**
   - Disable debug mode in production
   - Check log file sizes
   - Verify log rotation is working

### Debug Commands

```python
# Check logger status
logger = get_stage10_logger()
print(f"Debug mode: {logger.debug_mode}")
print(f"Log file: {logger.log_file}")

# Manual log export
export_file = export_logs()
print(f"Logs exported to: {export_file}")
```

## Future Enhancements

### Planned Features
- **Log Analytics Dashboard** - Visual analytics for log data
- **Performance Metrics** - Automated performance monitoring
- **Alert System** - Notifications for critical errors
- **Log Aggregation** - Centralized logging across all stages
- **Remote Logging** - Send logs to external monitoring systems

### Configuration Options
- **Log Level Configuration** - Configurable minimum log levels
- **Custom Log Formats** - User-defined log formatting
- **Log Filtering** - Advanced filtering and search capabilities
- **Retention Policies** - Automated log cleanup and archival

---

## Quick Reference

### Environment Variables
- `SCRIPTWEAVER_DEBUG=true` - Enable debug mode

### Key Functions
- `log_gap_analysis(event_type, **kwargs)` - Gap analysis events
- `log_interactive_selector(event_type, gap_id, **kwargs)` - Selector events
- `log_script_generation(event_type, **kwargs)` - Script generation events
- `log_session_state(event_type, key, value, **kwargs)` - State events
- `log_ui_interaction(interaction_type, component, **kwargs)` - UI events
- `log_process(event_type, **kwargs)` - Process events
- `log_ai_request(request_type, model, prompt_length, **kwargs)` - AI events
- `log_operation(operation_name, context)` - Context manager for operations
- `display_debug_panel()` - Show debug panel in UI
- `export_logs()` - Export logs to file

### Log Contexts
- `gap_analysis` - Gap analysis operations
- `interactive_selector` - Interactive element selection
- `script_generation` - Script generation workflows
- `session_state` - Session state management
- `ui` - User interface interactions
- `process` - Subprocess management
- `ai` - AI API interactions
- `stage10` - General Stage 10 operations

---

*For more information, see the test script `test_enhanced_logging.py` for comprehensive examples of all logging features.*
