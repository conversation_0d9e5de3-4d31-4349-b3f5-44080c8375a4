"""
Enhanced logging system for Stage 10 (Script Playground) of GretahAI ScriptWeaver.

This module provides comprehensive logging capabilities for all Stage 10 workflows including:
- Interactive element selector operations
- Gap analysis processes
- Script generation workflows
- Session state management
- User interface interactions
"""

import os
import sys
import json
import time
import logging
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from contextlib import contextmanager

import streamlit as st

# Import debug utilities
from debug_utils import DEBUG_MODE

class Stage10Logger:
    """Enhanced logger for Stage 10 operations with multiple output targets."""
    
    def __init__(self):
        self.logger = logging.getLogger("ScriptWeaver.stage10.enhanced")
        self.debug_mode = DEBUG_MODE
        self.log_dir = Path("debug_logs/stage10")
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Create session-specific log file
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.log_dir / f"stage10_session_{self.session_id}.log"
        
        # Initialize session state for UI logging
        if 'stage10_debug_logs' not in st.session_state:
            st.session_state.stage10_debug_logs = []
        
        # Setup file handler
        self._setup_file_logging()
        
        self.info("Stage 10 Enhanced Logger initialized", context="system")
    
    def _setup_file_logging(self):
        """Setup file-based logging with proper formatting."""
        # Remove existing handlers to avoid duplicates
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Create file handler
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # Create detailed formatter
        formatter = logging.Formatter(
            '%(asctime)s.%(msecs)03d - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.DEBUG)
    
    def _log_to_ui(self, level: str, message: str, context: str = None, data: Dict = None):
        """Log message to Streamlit UI for real-time debugging."""
        if not self.debug_mode:
            return
        
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        log_entry = {
            'timestamp': timestamp,
            'level': level,
            'context': context or 'general',
            'message': message,
            'data': data
        }
        
        # Add to session state (keep last 100 entries)
        st.session_state.stage10_debug_logs.append(log_entry)
        if len(st.session_state.stage10_debug_logs) > 100:
            st.session_state.stage10_debug_logs.pop(0)
    
    def debug(self, message: str, context: str = None, data: Dict = None):
        """Log debug message."""
        self.logger.debug(f"[{context or 'DEBUG'}] {message}")
        self._log_to_ui("DEBUG", message, context, data)
        
        if self.debug_mode:
            print(f"STAGE10-DEBUG [{context or 'DEBUG'}]: {message}")
    
    def info(self, message: str, context: str = None, data: Dict = None):
        """Log info message."""
        self.logger.info(f"[{context or 'INFO'}] {message}")
        self._log_to_ui("INFO", message, context, data)
        
        if self.debug_mode:
            print(f"STAGE10-INFO [{context or 'INFO'}]: {message}")
    
    def warning(self, message: str, context: str = None, data: Dict = None):
        """Log warning message."""
        self.logger.warning(f"[{context or 'WARNING'}] {message}")
        self._log_to_ui("WARNING", message, context, data)
        
        if self.debug_mode:
            print(f"STAGE10-WARNING [{context or 'WARNING'}]: {message}")
    
    def error(self, message: str, context: str = None, data: Dict = None, exc_info: bool = False):
        """Log error message."""
        self.logger.error(f"[{context or 'ERROR'}] {message}", exc_info=exc_info)
        self._log_to_ui("ERROR", message, context, data)
        
        if self.debug_mode:
            print(f"STAGE10-ERROR [{context or 'ERROR'}]: {message}")
    
    def log_interactive_selector_event(self, event_type: str, gap_id: str, details: Dict = None):
        """Log interactive selector specific events."""
        context = f"interactive_selector.{event_type}"
        message = f"Gap {gap_id}: {event_type}"
        
        event_data = {
            'gap_id': gap_id,
            'event_type': event_type,
            'timestamp': time.time(),
            'details': details or {}
        }
        
        self.info(message, context, event_data)
    
    def log_gap_analysis_event(self, event_type: str, details: Dict = None):
        """Log gap analysis specific events."""
        context = f"gap_analysis.{event_type}"
        message = f"Gap analysis: {event_type}"
        
        event_data = {
            'event_type': event_type,
            'timestamp': time.time(),
            'details': details or {}
        }
        
        self.info(message, context, event_data)
    
    def log_script_generation_event(self, event_type: str, details: Dict = None):
        """Log script generation specific events."""
        context = f"script_generation.{event_type}"
        message = f"Script generation: {event_type}"
        
        event_data = {
            'event_type': event_type,
            'timestamp': time.time(),
            'details': details or {}
        }
        
        self.info(message, context, event_data)
    
    def log_session_state_event(self, event_type: str, key: str, value: Any = None, details: Dict = None):
        """Log session state management events."""
        context = f"session_state.{event_type}"
        message = f"Session state {event_type}: {key}"
        
        event_data = {
            'event_type': event_type,
            'key': key,
            'value_type': type(value).__name__ if value is not None else None,
            'timestamp': time.time(),
            'details': details or {}
        }
        
        # Don't log the actual value if it's large or sensitive
        if value is not None and len(str(value)) < 200:
            event_data['value'] = str(value)
        
        self.debug(message, context, event_data)
    
    def log_ui_interaction(self, interaction_type: str, component: str, details: Dict = None):
        """Log user interface interactions."""
        context = f"ui.{interaction_type}"
        message = f"UI {interaction_type}: {component}"
        
        event_data = {
            'interaction_type': interaction_type,
            'component': component,
            'timestamp': time.time(),
            'details': details or {}
        }
        
        self.debug(message, context, event_data)
    
    def log_process_event(self, event_type: str, process_info: Dict = None):
        """Log subprocess and process management events."""
        context = f"process.{event_type}"
        message = f"Process {event_type}"
        
        event_data = {
            'event_type': event_type,
            'timestamp': time.time(),
            'process_info': process_info or {}
        }
        
        self.info(message, context, event_data)
    
    def log_ai_request(self, request_type: str, model: str, prompt_length: int, 
                      response_length: int = None, duration_ms: float = None, 
                      error: str = None):
        """Log AI API requests and responses."""
        context = f"ai.{request_type}"
        
        if error:
            message = f"AI {request_type} failed: {error}"
            level = "error"
        else:
            message = f"AI {request_type} completed"
            level = "info"
        
        event_data = {
            'request_type': request_type,
            'model': model,
            'prompt_length': prompt_length,
            'response_length': response_length,
            'duration_ms': duration_ms,
            'error': error,
            'timestamp': time.time()
        }
        
        if level == "error":
            self.error(message, context, event_data)
        else:
            self.info(message, context, event_data)
    
    @contextmanager
    def log_operation(self, operation_name: str, context: str = None):
        """Context manager for logging operation start/end with timing."""
        start_time = time.time()
        operation_context = context or operation_name
        
        self.info(f"Starting operation: {operation_name}", operation_context)
        
        try:
            yield
            duration_ms = (time.time() - start_time) * 1000
            self.info(f"Completed operation: {operation_name} ({duration_ms:.2f}ms)", operation_context)
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            self.error(f"Failed operation: {operation_name} ({duration_ms:.2f}ms) - {str(e)}", 
                      operation_context, exc_info=True)
            raise
    
    def display_debug_panel(self):
        """Display debug panel in Streamlit UI when debug mode is enabled."""
        if not self.debug_mode or not st.session_state.stage10_debug_logs:
            return
        
        with st.expander("🔍 Stage 10 Debug Logs", expanded=False):
            st.markdown("**Real-time debug information for Stage 10 operations**")
            
            # Filter controls
            col1, col2 = st.columns(2)
            with col1:
                level_filter = st.selectbox(
                    "Log Level",
                    options=["ALL", "DEBUG", "INFO", "WARNING", "ERROR"],
                    index=0,
                    key="stage10_log_level_filter"
                )
            
            with col2:
                context_filter = st.selectbox(
                    "Context",
                    options=["ALL"] + list(set(log['context'] for log in st.session_state.stage10_debug_logs)),
                    index=0,
                    key="stage10_log_context_filter"
                )
            
            # Filter logs
            filtered_logs = st.session_state.stage10_debug_logs
            
            if level_filter != "ALL":
                filtered_logs = [log for log in filtered_logs if log['level'] == level_filter]
            
            if context_filter != "ALL":
                filtered_logs = [log for log in filtered_logs if log['context'] == context_filter]
            
            # Display logs (most recent first)
            for log_entry in reversed(filtered_logs[-20:]):  # Show last 20 entries
                level_color = {
                    'DEBUG': '🔍',
                    'INFO': 'ℹ️',
                    'WARNING': '⚠️',
                    'ERROR': '❌'
                }.get(log_entry['level'], '📝')
                
                st.code(f"{level_color} [{log_entry['timestamp']}] [{log_entry['context']}] {log_entry['message']}")
                
                if log_entry.get('data'):
                    with st.expander(f"Data for {log_entry['timestamp']}", expanded=False):
                        st.json(log_entry['data'])
    
    def export_logs(self) -> str:
        """Export current session logs to a file."""
        export_file = self.log_dir / f"stage10_export_{self.session_id}.json"
        
        export_data = {
            'session_id': self.session_id,
            'export_timestamp': datetime.now().isoformat(),
            'logs': st.session_state.stage10_debug_logs,
            'log_file_path': str(self.log_file)
        }
        
        with open(export_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        self.info(f"Logs exported to {export_file}", "system")
        return str(export_file)

# Global logger instance
_stage10_logger = None

def get_stage10_logger() -> Stage10Logger:
    """Get the global Stage 10 logger instance."""
    global _stage10_logger
    if _stage10_logger is None:
        _stage10_logger = Stage10Logger()
    return _stage10_logger

# Convenience functions for common logging operations
def log_interactive_selector(event_type: str, gap_id: str, **kwargs):
    """Log interactive selector event."""
    get_stage10_logger().log_interactive_selector_event(event_type, gap_id, kwargs)

def log_gap_analysis(event_type: str, **kwargs):
    """Log gap analysis event."""
    get_stage10_logger().log_gap_analysis_event(event_type, kwargs)

def log_script_generation(event_type: str, **kwargs):
    """Log script generation event."""
    get_stage10_logger().log_script_generation_event(event_type, kwargs)

def log_session_state(event_type: str, key: str, value: Any = None, **kwargs):
    """Log session state event."""
    get_stage10_logger().log_session_state_event(event_type, key, value, kwargs)

def log_ui_interaction(interaction_type: str, component: str, **kwargs):
    """Log UI interaction."""
    get_stage10_logger().log_ui_interaction(interaction_type, component, kwargs)

def log_process(event_type: str, **kwargs):
    """Log process event."""
    get_stage10_logger().log_process_event(event_type, kwargs)

def log_ai_request(request_type: str, model: str, prompt_length: int, **kwargs):
    """Log AI request."""
    get_stage10_logger().log_ai_request(request_type, model, prompt_length, **kwargs)

def log_operation(operation_name: str, context: str = None):
    """Context manager for logging operations."""
    return get_stage10_logger().log_operation(operation_name, context)

def display_debug_panel():
    """Display the debug panel in Streamlit UI."""
    get_stage10_logger().display_debug_panel()

def export_logs() -> str:
    """Export current session logs."""
    return get_stage10_logger().export_logs()
