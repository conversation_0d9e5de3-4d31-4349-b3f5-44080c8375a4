#!/usr/bin/env python3
"""
Test script to verify the interactive selector gap analysis fix in Stage 10.

This script simulates the gap analysis workflow and tests that the "👆 Select" 
buttons work correctly without being blocked by the rerun prevention logic.
"""

import streamlit as st
import sys
import os
import time
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gap_analysis_interactive_selector():
    """Test the gap analysis interactive selector workflow."""
    
    st.title("🧪 Gap Analysis Interactive Selector Test")
    st.markdown("This test verifies that the '👆 Select' buttons in gap analysis forms work correctly.")
    
    # Test configuration
    st.subheader("Test Configuration")
    
    # Website URL configuration
    website_url = st.text_input(
        "Website URL for Testing",
        value="https://example.com",
        help="Enter a website URL to test interactive element selection"
    )
    
    # Test gap data
    test_gaps = [
        {
            'id': 'test_locator_1',
            'type': 'locator',
            'description': 'Username input field locator',
            'suggested_values': ['#username', '[name="username"]', '.username-input'],
            'required': True
        },
        {
            'id': 'test_locator_2', 
            'type': 'locator',
            'description': 'Submit button locator',
            'suggested_values': ['#submit', '[type="submit"]', '.submit-btn'],
            'required': True
        },
        {
            'id': 'test_data_1',
            'type': 'test_data',
            'description': 'Test username value',
            'suggested_values': ['testuser', 'admin', 'user123'],
            'required': True
        }
    ]
    
    st.subheader("Test Status")
    
    # Check for pending interactive selector requests
    pending_keys = [k for k in st.session_state.keys() if k.startswith('interactive_selector_pending_')]
    if pending_keys:
        st.warning(f"⏳ Found {len(pending_keys)} pending interactive selector requests:")
        for key in pending_keys:
            data = st.session_state[key]
            st.code(f"{key}: {data}")
    
    # Check for active interactive selector flags
    active_keys = [k for k in st.session_state.keys() if k.startswith('interactive_selector_in_progress')]
    if active_keys:
        st.info(f"🎯 Found {len(active_keys)} active interactive selector flags:")
        for key in active_keys:
            st.code(f"{key}: {st.session_state[key]}")
    
    # Check for selected locators
    locator_keys = [k for k in st.session_state.keys() if k.startswith('interactive_locator_')]
    if locator_keys:
        st.success(f"✅ Found {len(locator_keys)} selected locators:")
        for key in locator_keys:
            st.code(f"{key}: {st.session_state[key]}")
    
    # Test the gap filling form
    st.subheader("Gap Analysis Form Test")
    
    try:
        from core.gap_analysis import display_gap_filling_form
        
        st.markdown("**Testing gap filling form with interactive selector:**")
        
        # Use a unique form key for testing
        form_key = "test_gap_form"
        
        # Call the gap filling form function
        result = display_gap_filling_form(test_gaps, form_key, website_url)
        
        if result is not None:
            st.success("✅ Form submitted successfully!")
            st.json(result)
        else:
            st.info("⏳ Form not yet submitted or interactive selector in progress")
            
    except Exception as e:
        st.error(f"❌ Error testing gap filling form: {e}")
        st.exception(e)
    
    # Manual testing controls
    st.subheader("Manual Testing Controls")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🧪 Set Pending Request", help="Manually set a pending interactive selector request"):
            gap_id = "manual_test_gap"
            st.session_state[f'interactive_selector_pending_{gap_id}'] = {
                'gap_id': gap_id,
                'description': 'Manual test locator',
                'website_url': website_url,
                'timestamp': time.time()
            }
            st.success(f"✅ Set pending request for gap {gap_id}")
            st.rerun()
    
    with col2:
        if st.button("🎯 Set Active Flag", help="Manually set an active interactive selector flag"):
            st.session_state['interactive_selector_in_progress'] = True
            st.session_state['interactive_selector_in_progress_manual_test'] = True
            st.success("✅ Set active interactive selector flags")
            st.rerun()
    
    with col3:
        if st.button("🗑️ Clear All Flags", help="Clear all interactive selector related flags"):
            keys_to_clear = [k for k in st.session_state.keys() 
                           if k.startswith(('interactive_selector_', 'interactive_locator_'))]
            for key in keys_to_clear:
                del st.session_state[key]
            st.success(f"✅ Cleared {len(keys_to_clear)} interactive selector flags")
            st.rerun()
    
    # Rerun counter
    st.subheader("Rerun Monitoring")
    
    if 'test_rerun_counter' not in st.session_state:
        st.session_state['test_rerun_counter'] = 0
    
    st.session_state['test_rerun_counter'] += 1
    
    st.metric("Page Reruns", st.session_state['test_rerun_counter'])
    
    if st.session_state['test_rerun_counter'] > 10:
        st.error("❌ Too many reruns detected! This may indicate an issue with the fix.")
    elif st.session_state['test_rerun_counter'] > 5:
        st.warning(f"⚠️ {st.session_state['test_rerun_counter']} reruns detected")
    else:
        st.success("✅ Normal rerun count")
    
    # Debug information
    st.subheader("Debug Information")
    
    with st.expander("Session State Analysis", expanded=False):
        all_keys = list(st.session_state.keys())
        st.write(f"**Total session state keys:** {len(all_keys)}")
        
        # Group keys by category
        interactive_keys = [k for k in all_keys if 'interactive' in k.lower()]
        gap_keys = [k for k in all_keys if 'gap' in k.lower()]
        test_keys = [k for k in all_keys if 'test' in k.lower()]
        
        st.write(f"**Interactive-related keys:** {len(interactive_keys)}")
        for key in interactive_keys:
            st.code(f"{key}: {type(st.session_state[key]).__name__}")
        
        st.write(f"**Gap-related keys:** {len(gap_keys)}")
        for key in gap_keys:
            st.code(f"{key}: {type(st.session_state[key]).__name__}")
        
        st.write(f"**Test-related keys:** {len(test_keys)}")
        for key in test_keys:
            st.code(f"{key}: {type(st.session_state[key]).__name__}")
    
    # Instructions
    st.subheader("Testing Instructions")
    
    st.markdown("""
    **How to test the fix:**
    
    1. **Configure Website URL**: Enter a valid website URL above (e.g., https://google.com)
    
    2. **Test Interactive Selector Buttons**: 
       - Look for "👆 Select" buttons in the gap analysis form above
       - Click a "👆 Select" button
       - Verify that a browser window opens (may take a few seconds)
       - Check that the page doesn't get stuck in automatic reruns
    
    3. **Monitor Rerun Counter**: 
       - The rerun counter should increase by 1-2 when you click a button
       - It should NOT continuously increase every few seconds
    
    4. **Check Session State**: 
       - After clicking a button, check the "Test Status" section
       - You should see pending or active interactive selector flags
       - These flags should be cleaned up after the browser operation
    
    **Expected Behavior:**
    - ✅ Browser window opens when clicking "👆 Select"
    - ✅ Page shows "Interactive element selector is active" message
    - ✅ No automatic reruns every 2-8 seconds
    - ✅ Flags are properly managed and cleaned up
    
    **Problem Indicators:**
    - ❌ Browser window doesn't open
    - ❌ Continuous automatic reruns
    - ❌ Page gets stuck or unresponsive
    - ❌ Error messages about interactive selector
    """)

if __name__ == "__main__":
    test_gap_analysis_interactive_selector()
