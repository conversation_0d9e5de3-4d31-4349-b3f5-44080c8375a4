"""
Test script to validate the enhanced gap analysis implementation with interactive selector integration.

This script tests the enhanced gap analysis functionality that includes interactive
element selection for locator gaps in Stage 10.
"""

import json
import sys
import os

# Add the current directory to the path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.gap_analysis import (
    analyze_template_gaps,
    display_gap_filling_form,
    _render_locator_gap_input,
    _extract_best_locator,
    _handle_interactive_selector_results
)

def test_locator_extraction():
    """Test the locator extraction functionality."""
    print("🧪 Testing locator extraction from selected elements...")
    
    # Test cases for different element types
    test_elements = [
        {
            'name': 'Element with ID',
            'element': {
                'id': 'username',
                'name': 'user',
                'css_selector': '#username',
                'xpath': '//*[@id="username"]',
                'class_name': 'form-input',
                'tag_name': 'input'
            },
            'expected': '#username'
        },
        {
            'name': 'Element with name only',
            'element': {
                'name': 'password',
                'css_selector': '[name="password"]',
                'xpath': '//*[@name="password"]',
                'class_name': 'form-input password-field',
                'tag_name': 'input'
            },
            'expected': '[name=\'password\']'
        },
        {
            'name': 'Element with CSS selector',
            'element': {
                'css_selector': '.submit-button',
                'xpath': '//button[@class="submit-button"]',
                'class_name': 'submit-button',
                'tag_name': 'button'
            },
            'expected': '.submit-button'
        },
        {
            'name': 'Element with XPath only',
            'element': {
                'xpath': '//div[@data-testid="login-form"]',
                'tag_name': 'div'
            },
            'expected': '//div[@data-testid="login-form"]'
        },
        {
            'name': 'Element with multiple classes',
            'element': {
                'class_name': 'btn btn-primary submit-button',
                'tag_name': 'button'
            },
            'expected': '.btn'
        },
        {
            'name': 'Element with tag name only',
            'element': {
                'tag_name': 'button'
            },
            'expected': 'button'
        }
    ]
    
    passed = 0
    total = len(test_elements)
    
    for test_case in test_elements:
        element_name = test_case['name']
        element = test_case['element']
        expected = test_case['expected']
        
        result = _extract_best_locator(element)
        
        if result == expected:
            print(f"  ✅ {element_name}: {result}")
            passed += 1
        else:
            print(f"  ❌ {element_name}: Expected '{expected}', got '{result}'")
    
    print(f"\n📊 Locator extraction: {passed}/{total} tests passed")
    return passed == total


def test_gap_analysis_with_locators():
    """Test gap analysis that includes locator gaps."""
    print("\n🧪 Testing gap analysis with locator gaps...")
    
    # Sample template script
    template_script = {
        'id': 'template_login',
        'content': '''
def test_login():
    driver.get("https://example.com/login")
    
    # Enter username
    username_field = driver.find_element(By.ID, "username")
    username_field.send_keys("testuser")
    
    # Enter password  
    password_field = driver.find_element(By.ID, "password")
    password_field.send_keys("testpass")
    
    # Click login
    login_button = driver.find_element(By.ID, "login-btn")
    login_button.click()
        ''',
        'metadata': {
            'test_case_id': 'TC_001',
            'objective': 'Test basic login functionality'
        }
    }
    
    # Sample target test case with different requirements
    target_test_case = {
        'Test Case ID': 'TC_010',
        'Test Case Objective': 'Test login with custom form elements',
        'Steps': [
            {
                'Step No': 1,
                'action': 'Navigate to custom login page',
                'expected_result': 'Custom login form is displayed'
            },
            {
                'Step No': 2,
                'action': 'Enter email address in custom email field',
                'expected_result': 'Email is accepted'
            },
            {
                'Step No': 3,
                'action': 'Enter password in custom password field',
                'expected_result': 'Password is accepted'
            },
            {
                'Step No': 4,
                'action': 'Click custom submit button',
                'expected_result': 'Login is successful'
            }
        ]
    }
    
    # Mock AI response with locator gaps
    mock_response = '''
    ```json
    {
      "gaps": [
        {
          "id": "gap_email_field",
          "type": "locator",
          "description": "Email field selector for custom login form",
          "required": true,
          "suggested_values": ["#email", ".email-input", "[name='email']"],
          "impact": "high",
          "category": "ui_interaction"
        },
        {
          "id": "gap_password_field",
          "type": "locator", 
          "description": "Password field selector for custom form",
          "required": true,
          "suggested_values": ["#password", ".password-input", "[type='password']"],
          "impact": "high",
          "category": "ui_interaction"
        },
        {
          "id": "gap_submit_button",
          "type": "locator",
          "description": "Submit button selector for custom form",
          "required": true,
          "suggested_values": ["#submit", ".submit-btn", "button[type='submit']"],
          "impact": "high",
          "category": "ui_interaction"
        },
        {
          "id": "gap_custom_url",
          "type": "url",
          "description": "Custom login page URL",
          "required": true,
          "suggested_values": ["/custom-login", "/auth/login", "/signin"],
          "impact": "high",
          "category": "navigation"
        }
      ],
      "analysis_summary": "Found 4 gaps including 3 locator gaps that need custom selectors",
      "confidence_level": "high",
      "recommendations": [
        "Use interactive element selection for locator gaps",
        "Verify custom form structure matches template expectations"
      ]
    }
    ```
    '''
    
    # Parse the mock response
    from core.gap_analysis import _parse_gap_analysis_response, _validate_gap_analysis_result
    
    parsed_result = _parse_gap_analysis_response(mock_response)
    if parsed_result:
        validated_result = _validate_gap_analysis_result(parsed_result)
        gaps = validated_result.get('gaps', [])
        
        locator_gaps = [gap for gap in gaps if gap.get('type') == 'locator']
        
        print(f"✅ Gap analysis successful")
        print(f"📊 Total gaps: {len(gaps)}")
        print(f"🎯 Locator gaps: {len(locator_gaps)}")
        
        # Verify locator gaps have the expected structure
        for gap in locator_gaps:
            gap_id = gap.get('id', 'unknown')
            description = gap.get('description', 'No description')
            suggested_values = gap.get('suggested_values', [])
            
            print(f"  🔧 {gap_id}: {description}")
            print(f"    💡 Suggestions: {suggested_values}")
        
        return True
    else:
        print("❌ Gap analysis parsing failed")
        return False


def test_interactive_selector_integration():
    """Test the integration points for interactive selector."""
    print("\n🧪 Testing interactive selector integration...")
    
    try:
        # Test that we can import the interactive selector
        from core.interactive_selector import select_element_interactively
        print("✅ Interactive selector import successful")
        
        # Test helper functions exist
        assert callable(_extract_best_locator), "extract_best_locator not callable"
        assert callable(_handle_interactive_selector_results), "handle_interactive_selector_results not callable"
        print("✅ Helper functions are callable")
        
        # Test session state handling
        import streamlit as st
        
        # Mock session state for testing
        class MockSessionState:
            def __init__(self):
                self.data = {}
            
            def get(self, key, default=None):
                return self.data.get(key, default)
            
            def __setitem__(self, key, value):
                self.data[key] = value
            
            def __getitem__(self, key):
                return self.data[key]
            
            def __contains__(self, key):
                return key in self.data
            
            def __delitem__(self, key):
                if key in self.data:
                    del self.data[key]
            
            def keys(self):
                return self.data.keys()
        
        # Test session state operations
        mock_state = MockSessionState()
        mock_state['test_key'] = 'test_value'
        
        assert 'test_key' in mock_state
        assert mock_state.get('test_key') == 'test_value'
        print("✅ Session state operations working")
        
        return True
        
    except Exception as e:
        print(f"❌ Interactive selector integration test failed: {e}")
        return False


def test_enhanced_form_rendering():
    """Test that the enhanced form rendering works correctly."""
    print("\n🧪 Testing enhanced form rendering...")
    
    # Test gap with locator type
    test_gaps = [
        {
            'id': 'test_locator_gap',
            'type': 'locator',
            'description': 'Username field selector',
            'required': True,
            'suggested_values': ['#username', '.username-input', '[name="username"]']
        },
        {
            'id': 'test_data_gap',
            'type': 'test_data',
            'description': 'Test username value',
            'required': True,
            'suggested_values': ['testuser', 'admin', '<EMAIL>']
        }
    ]
    
    try:
        # Test that the form rendering functions exist and are callable
        assert callable(display_gap_filling_form), "display_gap_filling_form not callable"
        assert callable(_render_locator_gap_input), "render_locator_gap_input not callable"
        
        print("✅ Enhanced form rendering functions are callable")
        
        # Test gap type identification
        locator_gaps = [gap for gap in test_gaps if gap.get('type') == 'locator']
        data_gaps = [gap for gap in test_gaps if gap.get('type') == 'test_data']
        
        assert len(locator_gaps) == 1, f"Expected 1 locator gap, found {len(locator_gaps)}"
        assert len(data_gaps) == 1, f"Expected 1 data gap, found {len(data_gaps)}"
        
        print("✅ Gap type identification working")
        print(f"  🎯 Locator gaps: {len(locator_gaps)}")
        print(f"  🔧 Data gaps: {len(data_gaps)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced form rendering test failed: {e}")
        return False


def main():
    """Run all enhanced gap analysis tests."""
    print("🚀 Starting Enhanced Gap Analysis Tests")
    print("=" * 60)
    
    tests = [
        ("Locator Extraction", test_locator_extraction),
        ("Gap Analysis with Locators", test_gap_analysis_with_locators),
        ("Interactive Selector Integration", test_interactive_selector_integration),
        ("Enhanced Form Rendering", test_enhanced_form_rendering)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Enhanced Gap Analysis Test Results:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All enhanced gap analysis tests passed!")
        print("🔧 Interactive selector integration is ready for Stage 10.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
