"""
Debug script to test the gap analysis UI and interactive selector integration.

This script helps identify issues with the interactive selector button rendering
in Stage 10's gap analysis workflow.
"""

import sys
import os
import streamlit as st

# Add the current directory to the path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gap_analysis_ui():
    """Test the gap analysis UI components directly."""
    st.title("🔧 Gap Analysis UI Debug Tool")
    st.markdown("This tool helps debug the interactive selector integration in Stage 10.")
    
    # Test 1: Basic gap rendering
    st.header("Test 1: Basic Gap Rendering")
    
    # Mock gaps with different types including locators
    test_gaps = [
        {
            'id': 'test_locator_gap_1',
            'type': 'locator',
            'description': 'Username field selector',
            'required': True,
            'suggested_values': ['#username', '.username-input', '[name="username"]']
        },
        {
            'id': 'test_locator_gap_2',
            'type': 'locator',
            'description': 'Submit button selector',
            'required': True,
            'suggested_values': ['#submit', '.submit-btn', 'button[type="submit"]']
        },
        {
            'id': 'test_data_gap',
            'type': 'test_data',
            'description': 'Test username value',
            'required': True,
            'suggested_values': ['testuser', 'admin', '<EMAIL>']
        }
    ]
    
    # Test website URL input
    st.subheader("Website URL Configuration")
    website_url = st.text_input(
        "Website URL for Interactive Selection",
        value="https://example.com/login",
        help="Enter a valid website URL to enable interactive selector buttons"
    )
    
    st.info(f"Current website URL: `{website_url}`")
    st.info(f"Interactive selector enabled: {website_url and website_url != 'https://example.com'}")
    
    # Test gap rendering
    st.subheader("Gap Rendering Test")
    
    try:
        from core.gap_analysis import display_gap_filling_form
        
        # Display the gap filling form
        st.markdown("**Testing gap filling form with interactive selector:**")
        
        # Use a unique form key for testing
        form_key = "debug_test_form"
        
        # Call the gap filling form function
        result = display_gap_filling_form(test_gaps, form_key, website_url)
        
        if result is not None:
            st.success("✅ Form submitted successfully!")
            st.json(result)
        else:
            st.info("⏳ Form not yet submitted")
            
    except Exception as e:
        st.error(f"❌ Error testing gap filling form: {e}")
        st.exception(e)
    
    # Test 2: Direct locator input rendering
    st.header("Test 2: Direct Locator Input Rendering")
    
    try:
        from core.gap_analysis import _render_locator_gap_input
        
        st.markdown("**Testing direct locator input rendering:**")
        
        # Test with different scenarios
        test_scenarios = [
            {
                'name': 'With suggested values and valid URL',
                'gap_id': 'direct_test_1',
                'description': 'Login button selector',
                'suggested_values': ['#login', '.login-btn', 'button[type="submit"]'],
                'website_url': website_url if website_url != "https://example.com" else None
            },
            {
                'name': 'Without suggested values but valid URL',
                'gap_id': 'direct_test_2', 
                'description': 'Custom element selector',
                'suggested_values': [],
                'website_url': website_url if website_url != "https://example.com" else None
            },
            {
                'name': 'With suggested values but no URL',
                'gap_id': 'direct_test_3',
                'description': 'Element without interactive option',
                'suggested_values': ['#element', '.element-class'],
                'website_url': None
            }
        ]
        
        for i, scenario in enumerate(test_scenarios):
            st.subheader(f"Scenario {i+1}: {scenario['name']}")
            
            # Create a container for each test
            with st.container():
                st.markdown(f"**Gap ID:** `{scenario['gap_id']}`")
                st.markdown(f"**Description:** {scenario['description']}")
                st.markdown(f"**Suggested Values:** {scenario['suggested_values']}")
                st.markdown(f"**Website URL:** `{scenario['website_url']}`")
                
                # Test the locator input rendering
                try:
                    value = _render_locator_gap_input(
                        scenario['gap_id'],
                        scenario['description'],
                        scenario['suggested_values'],
                        scenario['website_url']
                    )
                    
                    if value:
                        st.success(f"✅ Value entered: `{value}`")
                    else:
                        st.info("⏳ No value entered yet")
                        
                except Exception as e:
                    st.error(f"❌ Error in scenario {i+1}: {e}")
                    st.exception(e)
                
                st.markdown("---")
    
    except Exception as e:
        st.error(f"❌ Error testing direct locator input: {e}")
        st.exception(e)
    
    # Test 3: Session state inspection
    st.header("Test 3: Session State Inspection")
    
    st.subheader("Interactive Selector Session State")
    interactive_keys = [key for key in st.session_state.keys() if 'interactive' in key.lower()]
    
    if interactive_keys:
        st.markdown("**Interactive selector related session state:**")
        for key in interactive_keys:
            st.markdown(f"- `{key}`: {st.session_state[key]}")
    else:
        st.info("No interactive selector session state found")
    
    st.subheader("Gap Analysis Session State")
    gap_keys = [key for key in st.session_state.keys() if 'gap' in key.lower()]
    
    if gap_keys:
        st.markdown("**Gap analysis related session state:**")
        for key in gap_keys:
            st.markdown(f"- `{key}`: {st.session_state[key]}")
    else:
        st.info("No gap analysis session state found")
    
    # Test 4: Import verification
    st.header("Test 4: Import Verification")
    
    try:
        from core.gap_analysis import (
            analyze_template_gaps,
            display_gap_filling_form,
            _render_locator_gap_input,
            _launch_interactive_selector_for_gap,
            _extract_best_locator,
            _handle_interactive_selector_results
        )
        st.success("✅ All gap analysis functions imported successfully")
        
        from core.interactive_selector import select_element_interactively
        st.success("✅ Interactive selector function imported successfully")
        
    except Exception as e:
        st.error(f"❌ Import error: {e}")
        st.exception(e)
    
    # Test 5: Manual button test
    st.header("Test 5: Manual Button Test")
    
    st.markdown("**Testing manual button rendering:**")
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        manual_input = st.text_input(
            "Manual Locator Input",
            placeholder="Enter a CSS selector or XPath",
            key="manual_locator_test"
        )
    
    with col2:
        if website_url and website_url != "https://example.com":
            if st.button("👆 Manual Test", key="manual_button_test", use_container_width=True):
                st.success("✅ Manual button clicked!")
                st.info(f"Would launch interactive selector for: {website_url}")
        else:
            st.button("👆 Manual Test", disabled=True, key="manual_button_disabled", use_container_width=True)
            st.warning("⚠️ Button disabled - need valid website URL")
    
    # Debug information
    st.header("Debug Information")
    
    st.subheader("Current Configuration")
    st.json({
        "website_url": website_url,
        "url_is_valid": website_url and website_url != "https://example.com",
        "session_state_keys": list(st.session_state.keys()),
        "test_gaps_count": len(test_gaps),
        "locator_gaps_count": len([g for g in test_gaps if g.get('type') == 'locator'])
    })


if __name__ == "__main__":
    test_gap_analysis_ui()
