#!/usr/bin/env python3
"""
Test script for the interactive element selector.

This script can be used to test the interactive selector functionality
independently of the main Streamlit application.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_interactive_selector():
    """Test the interactive selector with a simple webpage."""
    try:
        from core.interactive_selector import launch_interactive_selector
        
        print("Testing interactive element selector...")
        print("This will open a browser window to https://example.com")
        print("You can hover over elements and click to select them.")
        print("Press Ctrl+C to cancel the test.")
        
        # Test with example.com
        result = launch_interactive_selector("https://example.com")
        
        if result:
            print("\n✅ Element selection successful!")
            print(f"Selected element info: {result}")
        else:
            print("\n⚠️ No element was selected or selection was cancelled.")
            
    except KeyboardInterrupt:
        print("\n🛑 Test cancelled by user.")
    except Exception as e:
        print(f"\n❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

def test_webdriver_setup():
    """Test if WebDriver can be set up correctly."""
    try:
        from core.interactive_selector import setup_interactive_webdriver
        
        print("Testing WebDriver setup...")
        driver = setup_interactive_webdriver()
        
        if driver:
            print("✅ WebDriver setup successful!")
            print(f"Driver type: {type(driver)}")
            
            # Test navigation
            print("Testing navigation to example.com...")
            driver.get("https://example.com")
            print(f"Page title: {driver.title}")
            
            # Close the driver
            driver.quit()
            print("✅ WebDriver test completed successfully!")
        else:
            print("❌ WebDriver setup failed!")
            
    except Exception as e:
        print(f"❌ Error during WebDriver test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Interactive Selector Test Suite")
    print("=" * 40)
    
    # Test 1: WebDriver setup
    print("\n1. Testing WebDriver setup...")
    test_webdriver_setup()
    
    # Test 2: Interactive selector (optional)
    print("\n2. Testing interactive selector...")
    response = input("Do you want to test the interactive selector? (y/n): ")
    if response.lower() in ['y', 'yes']:
        test_interactive_selector()
    else:
        print("Skipping interactive selector test.")
    
    print("\nTest suite completed!")
