"""
Test script to validate the gap analysis implementation for Stage 10.

This script tests the core gap analysis functionality to ensure it works correctly
before integration with the full Stage 10 workflow.
"""

import json
import sys
import os

# Add the current directory to the path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.gap_analysis import (
    analyze_template_gaps,
    display_gap_filling_form,
    _build_gap_analysis_prompt,
    _parse_gap_analysis_response,
    _validate_gap_analysis_result
)

def test_prompt_building():
    """Test the gap analysis prompt building functionality."""
    print("🧪 Testing gap analysis prompt building...")
    
    # Sample template script
    template_script = {
        'id': 'template_001',
        'content': '''
import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By

def test_login():
    driver = webdriver.Chrome()
    driver.get("https://example.com/login")
    
    # Enter username
    username_field = driver.find_element(By.ID, "username")
    username_field.send_keys("testuser")
    
    # Enter password
    password_field = driver.find_element(By.ID, "password")
    password_field.send_keys("testpass")
    
    # Click login button
    login_button = driver.find_element(By.ID, "login-btn")
    login_button.click()
    
    # Verify login success
    assert "Dashboard" in driver.title
    
    driver.quit()
        ''',
        'metadata': {
            'test_case_id': 'TC_001',
            'objective': 'Test user login functionality'
        }
    }
    
    # Sample target test case
    target_test_case = {
        'Test Case ID': 'TC_005',
        'Test Case Objective': 'Test admin login with special characters',
        'Steps': [
            {
                'Step No': 1,
                'action': 'Navigate to admin login page',
                'expected_result': 'Admin login page is displayed'
            },
            {
                'Step No': 2,
                'action': 'Enter admin username with special characters',
                'expected_result': 'Username is accepted'
            },
            {
                'Step No': 3,
                'action': 'Enter complex password',
                'expected_result': 'Password is accepted'
            },
            {
                'Step No': 4,
                'action': 'Click admin login button',
                'expected_result': 'Admin dashboard is displayed'
            }
        ]
    }
    
    # Build the prompt
    prompt = _build_gap_analysis_prompt(template_script, target_test_case)
    
    if prompt:
        print("✅ Prompt building successful")
        print(f"📝 Prompt length: {len(prompt)} characters")
        print(f"🔍 Contains template content: {'template_001' in prompt}")
        print(f"🎯 Contains target test case: {'TC_005' in prompt}")
        return True
    else:
        print("❌ Prompt building failed")
        return False


def test_response_parsing():
    """Test the gap analysis response parsing functionality."""
    print("\n🧪 Testing gap analysis response parsing...")
    
    # Sample AI response
    sample_response = '''
    Based on my analysis, I found several gaps between the template and target test case:

    ```json
    {
      "gaps": [
        {
          "id": "gap_1",
          "type": "test_data",
          "description": "Admin username with special characters",
          "required": true,
          "suggested_values": ["<EMAIL>", "admin_user", "test.admin"],
          "impact": "high",
          "category": "authentication"
        },
        {
          "id": "gap_2",
          "type": "test_data",
          "description": "Complex admin password",
          "required": true,
          "suggested_values": ["Admin@123!", "P@ssw0rd!", "SecureP@ss1"],
          "impact": "high",
          "category": "authentication"
        },
        {
          "id": "gap_3",
          "type": "locator",
          "description": "Admin login button selector",
          "required": true,
          "suggested_values": ["#admin-login-btn", ".admin-login-button", "button[data-role='admin-login']"],
          "impact": "medium",
          "category": "ui_interaction"
        },
        {
          "id": "gap_4",
          "type": "url",
          "description": "Admin login page URL",
          "required": true,
          "suggested_values": ["/admin/login", "/admin", "/admin-portal"],
          "impact": "high",
          "category": "navigation"
        }
      ],
      "analysis_summary": "Found 4 critical gaps that need to be addressed for successful admin login script generation",
      "confidence_level": "high",
      "recommendations": [
        "Provide specific admin credentials for testing",
        "Verify admin-specific element selectors",
        "Confirm admin login page URL structure"
      ]
    }
    ```
    '''
    
    # Parse the response
    parsed_result = _parse_gap_analysis_response(sample_response)
    
    if parsed_result:
        print("✅ Response parsing successful")
        print(f"📊 Found {len(parsed_result.get('gaps', []))} gaps")
        print(f"🎯 Analysis summary: {parsed_result.get('analysis_summary', 'N/A')}")
        print(f"🔍 Confidence level: {parsed_result.get('confidence_level', 'N/A')}")
        
        # Validate the result structure
        validated_result = _validate_gap_analysis_result(parsed_result)
        print(f"✅ Validation successful: {len(validated_result.get('gaps', []))} validated gaps")
        
        return True
    else:
        print("❌ Response parsing failed")
        return False


def test_gap_types():
    """Test different gap types and their handling."""
    print("\n🧪 Testing different gap types...")
    
    gap_types = [
        {
            "id": "test_data_gap",
            "type": "test_data",
            "description": "Username for login",
            "required": True,
            "suggested_values": ["testuser", "admin", "<EMAIL>"]
        },
        {
            "id": "locator_gap",
            "type": "locator",
            "description": "Submit button selector",
            "required": True,
            "suggested_values": ["#submit", ".submit-btn", "button[type='submit']"]
        },
        {
            "id": "url_gap",
            "type": "url",
            "description": "Login page URL",
            "required": True,
            "suggested_values": ["/login", "/auth", "/signin"]
        },
        {
            "id": "config_gap",
            "type": "configuration",
            "description": "Timeout setting",
            "required": False,
            "suggested_values": ["10", "15", "30"]
        },
        {
            "id": "timeout_gap",
            "type": "timeout",
            "description": "Element wait timeout",
            "required": False,
            "suggested_values": []
        },
        {
            "id": "boolean_gap",
            "type": "boolean",
            "description": "Enable headless mode",
            "required": False,
            "suggested_values": []
        }
    ]
    
    print(f"✅ Testing {len(gap_types)} different gap types:")
    for gap in gap_types:
        gap_type = gap.get('type')
        description = gap.get('description')
        print(f"  🔧 {gap_type}: {description}")
    
    return True


def test_integration_readiness():
    """Test if the gap analysis module is ready for integration."""
    print("\n🧪 Testing integration readiness...")
    
    try:
        # Test imports
        from core.gap_analysis import analyze_template_gaps, display_gap_filling_form
        print("✅ Core functions imported successfully")
        
        # Test that the functions exist and are callable
        assert callable(analyze_template_gaps), "analyze_template_gaps is not callable"
        assert callable(display_gap_filling_form), "display_gap_filling_form is not callable"
        print("✅ Functions are callable")
        
        # Test that required dependencies are available
        import streamlit as st
        print("✅ Streamlit dependency available")
        
        from debug_utils import debug
        print("✅ Debug utilities available")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration readiness test failed: {e}")
        return False


def main():
    """Run all gap analysis tests."""
    print("🚀 Starting Gap Analysis Implementation Tests")
    print("=" * 60)
    
    tests = [
        ("Prompt Building", test_prompt_building),
        ("Response Parsing", test_response_parsing),
        ("Gap Types", test_gap_types),
        ("Integration Readiness", test_integration_readiness)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Gap analysis implementation is ready.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
